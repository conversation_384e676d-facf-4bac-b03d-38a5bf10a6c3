import os
from kks.aws_alarm_police import AlarmHandler


#############################
# Logging
#############################
from aws_lambda_powertools import Logger
from aws_lambda_powertools.logging import utils
logger = Logger()
utils.copy_config_to_registered_loggers(source_logger=logger,
                                        exclude={"botocore.auth",
                                                 "botocore",
                                                 "urllib3"})


#############################
# Main
#############################
@logger.inject_lambda_context
def run(event, context):
    alarm_handler = AlarmHandler(
        config_file="alarm_default_setting.yml",
        action=os.environ.get("CONFIG_ACTION"),
    )
    alarm_handler.set_alarms()
