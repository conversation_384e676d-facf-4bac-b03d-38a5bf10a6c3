Action: new

AlarmPatterns:
  # EKS Node CPU Utilization
  - MetricName: node_cpu_utilization
    Namespace: ContainerInsights
    Dimensions:
      - Name: ClusterName
        ExtractRegex: (?P<VALUE>saku-stage-eks)
    Reference: Resource
    AlarmActions: arn:aws:sns:ap-northeast-1:945098771571:chatbot_slack_p0
    OKActions: arn:aws:sns:ap-northeast-1:945098771571:chatbot_slack_p0
    Statistic: Maximum
    Period: 60
    EvaluationPeriods: 5
    DatapointsToAlarm: 5
    Threshold: 80
    ComparisonOperator: GreaterThanOrEqualToThreshold
    TreatMissingData: notBreaching

  # EKS Deployment Replicas Monitoring - replicas_ready should be >= 1
  - MetricName: replicas_ready
    Namespace: ContainerInsights
    Dimensions:
      - Name: ClusterName
        Value: saku-prod-eks
      - Name: Namespace
        Value: prod-luke
      - Name: PodName
        ExtractRegex: (?P<VALUE>prod-luke-public-api)
    Reference: CloudWatch
    AlarmDescription: "EKS deployment replicas_ready is less than 1 for PodName: prod-luke-public-api in Namespace: prod-luke, indicating pod availability issues. Check deployment status and pod health."
    AlarmActions: arn:aws:sns:ap-northeast-1:138046196805:jcl-p1-alarms
    OKActions: arn:aws:sns:ap-northeast-1:138046196805:jcl-p1-alarms
    Statistic: Maximum
    Period: 300
    EvaluationPeriods: 5
    DatapointsToAlarm: 5
    Threshold: 1
    ComparisonOperator: LessThanThreshold
    TreatMissingData: breaching
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
      - Key: Service
        Value: luke-public-api

  # EKS Deployment Replicas Monitoring - prod-luke-job-manager
  - MetricName: replicas_ready
    Namespace: ContainerInsights
    Dimensions:
      - Name: ClusterName
        Value: saku-prod-eks
      - Name: Namespace
        Value: prod-luke
      - Name: PodName
        ExtractRegex: (?P<VALUE>prod-luke-job-manager)
    Reference: CloudWatch
    AlarmDescription: "EKS deployment replicas_ready is less than 1 for PodName: prod-luke-job-manager in Namespace: prod-luke, indicating pod availability issues. Check deployment status and pod health."
    AlarmActions: arn:aws:sns:ap-northeast-1:138046196805:jcl-p1-alarms
    OKActions: arn:aws:sns:ap-northeast-1:138046196805:jcl-p1-alarms
    Statistic: Maximum
    Period: 300
    EvaluationPeriods: 5
    DatapointsToAlarm: 5
    Threshold: 1
    ComparisonOperator: LessThanThreshold
    TreatMissingData: breaching
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
      - Key: Service
        Value: luke-job-manager

  # EKS Deployment Replicas Monitoring - prod-luke-internal-api
  - MetricName: replicas_ready
    Namespace: ContainerInsights
    Dimensions:
      - Name: ClusterName
        Value: saku-prod-eks
      - Name: Namespace
        Value: prod-luke
      - Name: PodName
        ExtractRegex: (?P<VALUE>prod-luke-internal-api)
    Reference: CloudWatch
    AlarmDescription: "EKS deployment replicas_ready is less than 1 for PodName: prod-luke-internal-api in Namespace: prod-luke, indicating pod availability issues. Check deployment status and pod health."
    AlarmActions: arn:aws:sns:ap-northeast-1:138046196805:jcl-p1-alarms
    OKActions: arn:aws:sns:ap-northeast-1:138046196805:jcl-p1-alarms
    Statistic: Maximum
    Period: 300
    EvaluationPeriods: 5
    DatapointsToAlarm: 5
    Threshold: 1
    ComparisonOperator: LessThanThreshold
    TreatMissingData: breaching
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
      - Key: Service
        Value: luke-internal-api

  # EKS Deployment Replicas Monitoring - prod-luke-decider-priority
  - MetricName: replicas_ready
    Namespace: ContainerInsights
    Dimensions:
      - Name: ClusterName
        Value: saku-prod-eks
      - Name: Namespace
        Value: prod-luke
      - Name: PodName
        ExtractRegex: (?P<VALUE>prod-luke-decider-priority)
    Reference: CloudWatch
    AlarmDescription: "EKS deployment replicas_ready is less than 1 for PodName: prod-luke-decider-priority in Namespace: prod-luke, indicating pod availability issues. Check deployment status and pod health."
    AlarmActions: arn:aws:sns:ap-northeast-1:138046196805:jcl-p1-alarms
    OKActions: arn:aws:sns:ap-northeast-1:138046196805:jcl-p1-alarms
    Statistic: Maximum
    Period: 300
    EvaluationPeriods: 5
    DatapointsToAlarm: 5
    Threshold: 1
    ComparisonOperator: LessThanThreshold
    TreatMissingData: breaching
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
      - Key: Service
        Value: luke-decider-priority

  # EKS Deployment Replicas Monitoring - prod-luke-decider-premium
  - MetricName: replicas_ready
    Namespace: ContainerInsights
    Dimensions:
      - Name: ClusterName
        Value: saku-prod-eks
      - Name: Namespace
        Value: prod-luke
      - Name: PodName
        ExtractRegex: (?P<VALUE>prod-luke-decider-premium)
    Reference: CloudWatch
    AlarmDescription: "EKS deployment replicas_ready is less than 1 for PodName: prod-luke-decider-premium in Namespace: prod-luke, indicating pod availability issues. Check deployment status and pod health."
    AlarmActions: arn:aws:sns:ap-northeast-1:138046196805:jcl-p1-alarms
    OKActions: arn:aws:sns:ap-northeast-1:138046196805:jcl-p1-alarms
    Statistic: Maximum
    Period: 300
    EvaluationPeriods: 5
    DatapointsToAlarm: 5
    Threshold: 1
    ComparisonOperator: LessThanThreshold
    TreatMissingData: breaching
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
      - Key: Service
        Value: luke-decider-premium

  # EKS Deployment Replicas Monitoring - prod-luke-decider
  - MetricName: replicas_ready
    Namespace: ContainerInsights
    Dimensions:
      - Name: ClusterName
        Value: saku-prod-eks
      - Name: Namespace
        Value: prod-luke
      - Name: PodName
        ExtractRegex: (?P<VALUE>prod-luke-decider)
    Reference: CloudWatch
    AlarmDescription: "EKS deployment replicas_ready is less than 1 for PodName: prod-luke-decider in Namespace: prod-luke, indicating pod availability issues. Check deployment status and pod health."
    AlarmActions: arn:aws:sns:ap-northeast-1:138046196805:jcl-p1-alarms
    OKActions: arn:aws:sns:ap-northeast-1:138046196805:jcl-p1-alarms
    Statistic: Maximum
    Period: 300
    EvaluationPeriods: 5
    DatapointsToAlarm: 5
    Threshold: 1
    ComparisonOperator: LessThanThreshold
    TreatMissingData: breaching
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
      - Key: Service
        Value: luke-decider

  # EKS Deployment Replicas Monitoring - prod-coco-catchup-scheduler
  - MetricName: replicas_ready
    Namespace: ContainerInsights
    Dimensions:
      - Name: ClusterName
        Value: saku-prod-eks
      - Name: Namespace
        Value: prod-coco
      - Name: PodName
        ExtractRegex: (?P<VALUE>prod-coco-catchup-scheduler)
    Reference: CloudWatch
    AlarmDescription: "EKS deployment replicas_ready is less than 1 for PodName: prod-coco-catchup-scheduler in Namespace: prod-coco, indicating pod availability issues. Check deployment status and pod health."
    AlarmActions: arn:aws:sns:ap-northeast-1:138046196805:jcl-p1-alarms
    OKActions: arn:aws:sns:ap-northeast-1:138046196805:jcl-p1-alarms
    Statistic: Maximum
    Period: 300
    EvaluationPeriods: 5
    DatapointsToAlarm: 5
    Threshold: 1
    ComparisonOperator: LessThanThreshold
    TreatMissingData: breaching
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
      - Key: Service
        Value: coco-catchup-scheduler

  # EKS Deployment Replicas Monitoring - prod-coco-internal-api
  - MetricName: replicas_ready
    Namespace: ContainerInsights
    Dimensions:
      - Name: ClusterName
        Value: saku-prod-eks
      - Name: Namespace
        Value: prod-coco
      - Name: PodName
        ExtractRegex: (?P<VALUE>prod-coco-internal-api)
    Reference: CloudWatch
    AlarmDescription: "EKS deployment replicas_ready is less than 1 for PodName: prod-coco-internal-api in Namespace: prod-coco, indicating pod availability issues. Check deployment status and pod health."
    AlarmActions: arn:aws:sns:ap-northeast-1:138046196805:jcl-p1-alarms
    OKActions: arn:aws:sns:ap-northeast-1:138046196805:jcl-p1-alarms
    Statistic: Maximum
    Period: 300
    EvaluationPeriods: 5
    DatapointsToAlarm: 5
    Threshold: 1
    ComparisonOperator: LessThanThreshold
    TreatMissingData: breaching
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
      - Key: Service
        Value: coco-internal-api

  # EKS Deployment Replicas Monitoring - prod-coco-linear-automatic-switch
  - MetricName: replicas_ready
    Namespace: ContainerInsights
    Dimensions:
      - Name: ClusterName
        Value: saku-prod-eks
      - Name: Namespace
        Value: prod-coco
      - Name: PodName
        ExtractRegex: (?P<VALUE>prod-coco-linear-automatic-switch)
    Reference: CloudWatch
    AlarmDescription: "EKS deployment replicas_ready is less than 1 for PodName: prod-coco-linear-automatic-switch in Namespace: prod-coco, indicating pod availability issues. Check deployment status and pod health."
    AlarmActions: arn:aws:sns:ap-northeast-1:138046196805:jcl-p1-alarms
    OKActions: arn:aws:sns:ap-northeast-1:138046196805:jcl-p1-alarms
    Statistic: Maximum
    Period: 300
    EvaluationPeriods: 5
    DatapointsToAlarm: 5
    Threshold: 1
    ComparisonOperator: LessThanThreshold
    TreatMissingData: breaching
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
      - Key: Service
        Value: coco-linear-automatic-switch

  # EKS Deployment Replicas Monitoring - prod-coco-linear-tv-manager
  - MetricName: replicas_ready
    Namespace: ContainerInsights
    Dimensions:
      - Name: ClusterName
        Value: saku-prod-eks
      - Name: Namespace
        Value: prod-coco
      - Name: PodName
        ExtractRegex: (?P<VALUE>prod-coco-linear-tv-manager)
    Reference: CloudWatch
    AlarmDescription: "EKS deployment replicas_ready is less than 1 for PodName: prod-coco-linear-tv-manager in Namespace: prod-coco, indicating pod availability issues. Check deployment status and pod health."
    AlarmActions: arn:aws:sns:ap-northeast-1:138046196805:jcl-p1-alarms
    OKActions: arn:aws:sns:ap-northeast-1:138046196805:jcl-p1-alarms
    Statistic: Maximum
    Period: 300
    EvaluationPeriods: 5
    DatapointsToAlarm: 5
    Threshold: 1
    ComparisonOperator: LessThanThreshold
    TreatMissingData: breaching
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
      - Key: Service
        Value: coco-linear-tv-manager

  # EKS Deployment Replicas Monitoring - prod-coco-live-to-vod-status-checker
  - MetricName: replicas_ready
    Namespace: ContainerInsights
    Dimensions:
      - Name: ClusterName
        Value: saku-prod-eks
      - Name: Namespace
        Value: prod-coco
      - Name: PodName
        ExtractRegex: (?P<VALUE>prod-coco-live-to-vod-status-checker)
    Reference: CloudWatch
    AlarmDescription: "EKS deployment replicas_ready is less than 1 for PodName: prod-coco-live-to-vod-status-checker in Namespace: prod-coco, indicating pod availability issues. Check deployment status and pod health."
    AlarmActions: arn:aws:sns:ap-northeast-1:138046196805:jcl-p1-alarms
    OKActions: arn:aws:sns:ap-northeast-1:138046196805:jcl-p1-alarms
    Statistic: Maximum
    Period: 300
    EvaluationPeriods: 5
    DatapointsToAlarm: 5
    Threshold: 1
    ComparisonOperator: LessThanThreshold
    TreatMissingData: breaching
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
      - Key: Service
        Value: coco-live-to-vod-status-checker

  # EKS Deployment Replicas Monitoring - prod-coco-portal
  - MetricName: replicas_ready
    Namespace: ContainerInsights
    Dimensions:
      - Name: ClusterName
        Value: saku-prod-eks
      - Name: Namespace
        Value: prod-coco
      - Name: PodName
        ExtractRegex: (?P<VALUE>prod-coco-portal)
    Reference: CloudWatch
    AlarmDescription: "EKS deployment replicas_ready is less than 1 for PodName: prod-coco-portal in Namespace: prod-coco, indicating pod availability issues. Check deployment status and pod health."
    AlarmActions: arn:aws:sns:ap-northeast-1:138046196805:jcl-p1-alarms
    OKActions: arn:aws:sns:ap-northeast-1:138046196805:jcl-p1-alarms
    Statistic: Maximum
    Period: 300
    EvaluationPeriods: 5
    DatapointsToAlarm: 5
    Threshold: 1
    ComparisonOperator: LessThanThreshold
    TreatMissingData: breaching
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
      - Key: Service
        Value: coco-portal

  # EKS Deployment Replicas Monitoring - prod-jon-cms-portal
  - MetricName: replicas_ready
    Namespace: ContainerInsights
    Dimensions:
      - Name: ClusterName
        Value: saku-prod-eks
      - Name: Namespace
        Value: prod-jon
      - Name: PodName
        ExtractRegex: (?P<VALUE>prod-jon-cms-portal)
    Reference: CloudWatch
    AlarmDescription: "EKS deployment replicas_ready is less than 1 for PodName: prod-jon-cms-portal in Namespace: prod-jon, indicating pod availability issues. Check deployment status and pod health."
    AlarmActions: arn:aws:sns:ap-northeast-1:138046196805:jcl-p1-alarms
    OKActions: arn:aws:sns:ap-northeast-1:138046196805:jcl-p1-alarms
    Statistic: Maximum
    Period: 300
    EvaluationPeriods: 5
    DatapointsToAlarm: 5
    Threshold: 1
    ComparisonOperator: LessThanThreshold
    TreatMissingData: breaching
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
      - Key: Service
        Value: jon-cms-portal

  # EKS Deployment Replicas Monitoring - prod-jon-drm-monitor-fairplay
  - MetricName: replicas_ready
    Namespace: ContainerInsights
    Dimensions:
      - Name: ClusterName
        Value: saku-prod-eks
      - Name: Namespace
        Value: prod-jon
      - Name: PodName
        ExtractRegex: (?P<VALUE>prod-jon-drm-monitor-fairplay)
    Reference: CloudWatch
    AlarmDescription: "EKS deployment replicas_ready is less than 1 for PodName: prod-jon-drm-monitor-fairplay in Namespace: prod-jon, indicating pod availability issues. Check deployment status and pod health."
    AlarmActions: arn:aws:sns:ap-northeast-1:138046196805:jcl-p1-alarms
    OKActions: arn:aws:sns:ap-northeast-1:138046196805:jcl-p1-alarms
    Statistic: Maximum
    Period: 300
    EvaluationPeriods: 5
    DatapointsToAlarm: 5
    Threshold: 1
    ComparisonOperator: LessThanThreshold
    TreatMissingData: breaching
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
      - Key: Service
        Value: jon-drm-monitor-fairplay

  # EKS Deployment Replicas Monitoring - prod-jon-drm-monitor-widevine
  - MetricName: replicas_ready
    Namespace: ContainerInsights
    Dimensions:
      - Name: ClusterName
        Value: saku-prod-eks
      - Name: Namespace
        Value: prod-jon
      - Name: PodName
        ExtractRegex: (?P<VALUE>prod-jon-drm-monitor-widevine)
    Reference: CloudWatch
    AlarmDescription: "EKS deployment replicas_ready is less than 1 for PodName: prod-jon-drm-monitor-widevine in Namespace: prod-jon, indicating pod availability issues. Check deployment status and pod health."
    AlarmActions: arn:aws:sns:ap-northeast-1:138046196805:jcl-p1-alarms
    OKActions: arn:aws:sns:ap-northeast-1:138046196805:jcl-p1-alarms
    Statistic: Maximum
    Period: 300
    EvaluationPeriods: 5
    DatapointsToAlarm: 5
    Threshold: 1
    ComparisonOperator: LessThanThreshold
    TreatMissingData: breaching
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
      - Key: Service
        Value: jon-drm-monitor-widevine

  # EKS Deployment Replicas Monitoring - prod-jon-key-server
  - MetricName: replicas_ready
    Namespace: ContainerInsights
    Dimensions:
      - Name: ClusterName
        Value: saku-prod-eks
      - Name: Namespace
        Value: prod-jon
      - Name: PodName
        ExtractRegex: (?P<VALUE>prod-jon-key-server)
    Reference: CloudWatch
    AlarmDescription: "EKS deployment replicas_ready is less than 1 for PodName: prod-jon-key-server in Namespace: prod-jon, indicating pod availability issues. Check deployment status and pod health."
    AlarmActions: arn:aws:sns:ap-northeast-1:138046196805:jcl-p1-alarms
    OKActions: arn:aws:sns:ap-northeast-1:138046196805:jcl-p1-alarms
    Statistic: Maximum
    Period: 300
    EvaluationPeriods: 5
    DatapointsToAlarm: 5
    Threshold: 1
    ComparisonOperator: LessThanThreshold
    TreatMissingData: breaching
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
      - Key: Service
        Value: jon-key-server

  # EKS Deployment Replicas Monitoring - prod-jon-key-server-restful
  - MetricName: replicas_ready
    Namespace: ContainerInsights
    Dimensions:
      - Name: ClusterName
        Value: saku-prod-eks
      - Name: Namespace
        Value: prod-jon
      - Name: PodName
        ExtractRegex: (?P<VALUE>prod-jon-key-server)-restful
    Reference: CloudWatch
    AlarmDescription: "EKS deployment replicas_ready is less than 1 for PodName: prod-jon-key-server-restful in Namespace: prod-jon, indicating pod availability issues. Check deployment status and pod health."
    AlarmActions: arn:aws:sns:ap-northeast-1:138046196805:jcl-p1-alarms
    OKActions: arn:aws:sns:ap-northeast-1:138046196805:jcl-p1-alarms
    Statistic: Maximum
    Period: 300
    EvaluationPeriods: 5
    DatapointsToAlarm: 5
    Threshold: 1
    ComparisonOperator: LessThanThreshold
    TreatMissingData: breaching
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
      - Key: Service
        Value: jon-key-server-restful

  # EKS Deployment Replicas Monitoring - prod-jon-playready
  - MetricName: replicas_ready
    Namespace: ContainerInsights
    Dimensions:
      - Name: ClusterName
        Value: saku-prod-eks
      - Name: Namespace
        Value: prod-jon
      - Name: PodName
        ExtractRegex: (?P<VALUE>prod-jon-playready)
    Reference: CloudWatch
    AlarmDescription: "EKS deployment replicas_ready is less than 1 for PodName: prod-jon-playready in Namespace: prod-jon, indicating pod availability issues. Check deployment status and pod health."
    AlarmActions: arn:aws:sns:ap-northeast-1:138046196805:jcl-p1-alarms
    OKActions: arn:aws:sns:ap-northeast-1:138046196805:jcl-p1-alarms
    Statistic: Maximum
    Period: 300
    EvaluationPeriods: 5
    DatapointsToAlarm: 5
    Threshold: 1
    ComparisonOperator: LessThanThreshold
    TreatMissingData: breaching
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
      - Key: Service
        Value: jon-playready

  # EKS Deployment Replicas Monitoring - prod-jon-portal
  - MetricName: replicas_ready
    Namespace: ContainerInsights
    Dimensions:
      - Name: ClusterName
        Value: saku-prod-eks
      - Name: Namespace
        Value: prod-jon
      - Name: PodName
        ExtractRegex: (?P<VALUE>prod-jon-portal)
    Reference: CloudWatch
    AlarmDescription: "EKS deployment replicas_ready is less than 1 for PodName: prod-jon-portal in Namespace: prod-jon, indicating pod availability issues. Check deployment status and pod health."
    AlarmActions: arn:aws:sns:ap-northeast-1:138046196805:jcl-p1-alarms
    OKActions: arn:aws:sns:ap-northeast-1:138046196805:jcl-p1-alarms
    Statistic: Maximum
    Period: 300
    EvaluationPeriods: 5
    DatapointsToAlarm: 5
    Threshold: 1
    ComparisonOperator: LessThanThreshold
    TreatMissingData: breaching
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
      - Key: Service
        Value: jon-portal

  # EKS Deployment Replicas Monitoring - prod-jon-portal-restful
  - MetricName: replicas_ready
    Namespace: ContainerInsights
    Dimensions:
      - Name: ClusterName
        Value: saku-prod-eks
      - Name: Namespace
        Value: prod-jon
      - Name: PodName
        ExtractRegex: (?P<VALUE>prod-jon-portal)-restful
    Reference: CloudWatch
    AlarmDescription: "EKS deployment replicas_ready is less than 1 for PodName: prod-jon-portal-restful in Namespace: prod-jon, indicating pod availability issues. Check deployment status and pod health."
    AlarmActions: arn:aws:sns:ap-northeast-1:138046196805:jcl-p1-alarms
    OKActions: arn:aws:sns:ap-northeast-1:138046196805:jcl-p1-alarms
    Statistic: Maximum
    Period: 300
    EvaluationPeriods: 5
    DatapointsToAlarm: 5
    Threshold: 1
    ComparisonOperator: LessThanThreshold
    TreatMissingData: breaching
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
      - Key: Service
        Value: jon-portal-restful

  # EKS Deployment Replicas Monitoring - prod-jon-speke
  - MetricName: replicas_ready
    Namespace: ContainerInsights
    Dimensions:
      - Name: ClusterName
        Value: saku-prod-eks
      - Name: Namespace
        Value: prod-jon
      - Name: PodName
        ExtractRegex: (?P<VALUE>prod-jon-speke)
    Reference: CloudWatch
    AlarmDescription: "EKS deployment replicas_ready is less than 1 for PodName: prod-jon-speke in Namespace: prod-jon, indicating pod availability issues. Check deployment status and pod health."
    AlarmActions: arn:aws:sns:ap-northeast-1:138046196805:jcl-p1-alarms
    OKActions: arn:aws:sns:ap-northeast-1:138046196805:jcl-p1-alarms
    Statistic: Maximum
    Period: 300
    EvaluationPeriods: 5
    DatapointsToAlarm: 5
    Threshold: 1
    ComparisonOperator: LessThanThreshold
    TreatMissingData: breaching
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
      - Key: Service
        Value: jon-speke
  