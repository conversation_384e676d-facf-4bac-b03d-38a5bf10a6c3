Action: new

AlarmPatterns:
  # EKS Node CPU Utilization
  - MetricName: node_cpu_utilization
    Namespace: ContainerInsights
    Dimensions:
      - Name: ClusterName
        ExtractRegex: saku-stage-eks
    # Reference: Resource
    # AlarmActions: arn:aws:sns:ap-northeast-1:945098771571:chatbot_slack_p0
    # OKActions: arn:aws:sns:ap-northeast-1:945098771571:chatbot_slack_p0
    Statistic: Maximum
    Period: 60
    EvaluationPeriods: 5
    DatapointsToAlarm: 5
    Threshold: 80
    ComparisonOperator: GreaterThanOrEqualToThreshold
    TreatMissingData: notBreaching

  # EKS Deployment Replicas Monitoring - prod-luke-public-api (replicas_ready)
  - MetricName: replicas_ready
    Namespace: ContainerInsights
    Dimensions:
      - Name: ClusterName
        ExtractRegex: saku-prod-eks
      - Name: Namespace
        ExtractRegex: prod-luke
      - Name: PodName
        ExtractRegex: prod-luke-public-api
    # Reference: Resource
    # AlarmActions: arn:aws:sns:ap-northeast-1:138046196805:chatbot_slack_p0
    # OKActions: arn:aws:sns:ap-northeast-1:138046196805:chatbot_slack_p0
    Statistic: Maximum
    Period: 300
    EvaluationPeriods: 1
    DatapointsToAlarm: 1
    Threshold: 1
    ComparisonOperator: LessThanThreshold
    TreatMissingData: breaching

  # EKS Deployment Replicas Monitoring - prod-luke-job-manager (replicas_ready)
  - MetricName: replicas_ready
    Namespace: ContainerInsights
    Dimensions:
      - Name: ClusterName
        ExtractRegex: saku-prod-eks
      - Name: Namespace
        ExtractRegex: prod-luke
      - Name: PodName
        ExtractRegex: prod-luke-job-manager
    # Reference: Resource
    # AlarmActions: arn:aws:sns:ap-northeast-1:138046196805:chatbot_slack_p0
    # OKActions: arn:aws:sns:ap-northeast-1:138046196805:chatbot_slack_p0
    Statistic: Maximum
    Period: 300
    EvaluationPeriods: 1
    DatapointsToAlarm: 1
    Threshold: 1
    ComparisonOperator: LessThanThreshold
    TreatMissingData: breaching

  # EKS Deployment Replicas Monitoring - prod-luke-internal-api (replicas_ready)
  - MetricName: replicas_ready
    Namespace: ContainerInsights
    Dimensions:
      - Name: ClusterName
        ExtractRegex: saku-prod-eks
      - Name: Namespace
        ExtractRegex: prod-luke
      - Name: PodName
        ExtractRegex: prod-luke-internal-api
    # Reference: Resource
    # AlarmActions: arn:aws:sns:ap-northeast-1:138046196805:chatbot_slack_p0
    # OKActions: arn:aws:sns:ap-northeast-1:138046196805:chatbot_slack_p0
    Statistic: Maximum
    Period: 300
    EvaluationPeriods: 1
    DatapointsToAlarm: 1
    Threshold: 1
    ComparisonOperator: LessThanThreshold
    TreatMissingData: breaching

  # EKS Deployment Replicas Monitoring - prod-luke-decider-priority (replicas_ready)
  - MetricName: replicas_ready
    Namespace: ContainerInsights
    Dimensions:
      - Name: ClusterName
        ExtractRegex: saku-prod-eks
      - Name: Namespace
        ExtractRegex: prod-luke
      - Name: PodName
        ExtractRegex: prod-luke-decider-priority
    # Reference: Resource
    # AlarmActions: arn:aws:sns:ap-northeast-1:138046196805:chatbot_slack_p0
    # OKActions: arn:aws:sns:ap-northeast-1:138046196805:chatbot_slack_p0
    Statistic: Maximum
    Period: 300
    EvaluationPeriods: 1
    DatapointsToAlarm: 1
    Threshold: 1
    ComparisonOperator: LessThanThreshold
    TreatMissingData: breaching

  # EKS Deployment Replicas Monitoring - prod-luke-decider-premium (replicas_ready)
  - MetricName: replicas_ready
    Namespace: ContainerInsights
    Dimensions:
      - Name: ClusterName
        ExtractRegex: saku-prod-eks
      - Name: Namespace
        ExtractRegex: prod-luke
      - Name: PodName
        ExtractRegex: prod-luke-decider-premium
    # Reference: Resource
    # AlarmActions: arn:aws:sns:ap-northeast-1:138046196805:chatbot_slack_p0
    # OKActions: arn:aws:sns:ap-northeast-1:138046196805:chatbot_slack_p0
    Statistic: Maximum
    Period: 300
    EvaluationPeriods: 1
    DatapointsToAlarm: 1
    Threshold: 1
    ComparisonOperator: LessThanThreshold
    TreatMissingData: breaching

  # EKS Deployment Replicas Monitoring - prod-luke-decider (replicas_ready)
  - MetricName: replicas_ready
    Namespace: ContainerInsights
    Dimensions:
      - Name: ClusterName
        ExtractRegex: saku-prod-eks
      - Name: Namespace
        ExtractRegex: prod-luke
      - Name: PodName
        ExtractRegex: prod-luke-decider
    # Reference: Resource
    # AlarmActions: arn:aws:sns:ap-northeast-1:138046196805:chatbot_slack_p0
    # OKActions: arn:aws:sns:ap-northeast-1:138046196805:chatbot_slack_p0
    Statistic: Maximum
    Period: 300
    EvaluationPeriods: 1
    DatapointsToAlarm: 1
    Threshold: 1
    ComparisonOperator: LessThanThreshold
    TreatMissingData: breaching
  