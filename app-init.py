#!/usr/bin/env python3

#############################
# AWS CDK
#############################
from aws_cdk import App
from aws_cdk import Environment
from kks.cdk_construct import query_context


#############################
# Stack
#############################
from stack.init_stack import InitStack


#############################
# Construct
#############################
app = App()

stack_env = Environment(
    account=query_context(app, "aws.account"),
    region=query_context(app, "aws.region")
)

init_stack = InitStack(
    app, "{product}-{service}".format(
        product=query_context(app, "tags.Product"),
        service=query_context(app, "tags.Service")
    ),
    env=stack_env,
    termination_protection=True,
)

app.synth()
