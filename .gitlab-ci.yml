stages:
  - init
  - build
  - test
  - deploy

variables:
  CI: "true"
  GIT_DEPTH: 10
  PIP_ROOT_USER_ACTION: ignore
  PIP_DISABLE_PIP_VERSION_CHECK: 1
  JSII_SILENCE_WARNING_UNTESTED_NODE_VERSION: 1
  AWS_DEFAULT_REGION: ap-northeast-1

.deploy:
  stage: deploy
  script:
    - cdk diff ${stack}
    - cdk deploy ${stack} --require-approval never
  tags:
    - cdk

.dev:
  extends: .deploy
  variables:
    AWS_DEFAULT_REGION: ap-northeast-1
  before_script:
    - cp cdk-dev.json cdk.json
    - pip install -q -r requirements.txt

.prod:
  extends: .deploy
  variables:
    AWS_DEFAULT_REGION: ap-northeast-1
  before_script:
    - cp cdk-prod.json cdk.json
    - pip install -q -r requirements.txt

deploy:prod:
  extends: .prod
  variables:
    stack: --all
  resource_group: prod
  rules:
    - if: $CI_COMMIT_BRANCH == "prod"

deploy:chatops:prod:
  extends: .prod
  variables:
    stack: prod-saku-chatops
  resource_group: prod-saku-chatops
  rules:
    - if: $CI_COMMIT_BRANCH == "prod-saku-chatops"

deploy:ssm:prod:
  extends: .prod
  variables:
    stack: prod-saku-chatops-ssm
  resource_group: prod-saku-chatops-ssm
  rules:
    - if: $CI_COMMIT_BRANCH == "prod-saku-chatops-ssm"

deploy:alarm-police:prod:
  extends: .prod
  variables:
    stack: prod-saku-chatops-police
  resource_group: prod-saku-chatops-police
  rules:
    - if: $CI_COMMIT_BRANCH == "prod-saku-chatops-police"

test:alarm-police:
  stage: test
  before_script:
    - cd ${CI_PROJECT_DIR}/lambda_function/alarm_police/src
    - pip install -q -r ${CI_PROJECT_DIR}/lambda_function/alarm_police/src/requirements.txt
  script:
    - aws-police validate -c alarm_default_setting.yml
  rules:
    - if: $CI_PIPELINE_SOURCE == "push"
      changes:
        - lambda_function/alarm_police/**/*
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes:
        - lambda_function/alarm_police/**/*
  tags:
    - python3.13
