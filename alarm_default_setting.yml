Action: new

AlarmPatterns:
  # Application ELB
  - MetricName: TargetResponseTime
    Namespace: AWS/ApplicationELB
    Dimensions:
      - Name: LoadBalancer
        ExtractRegex: (?<=app/)(?P<VALUE>.*)(?=/.*)
      - Name: TargetGroup
        ExtractRegex: (?<=targetgroup/)(?P<VALUE>.*)(?=/.*)
    Reference: Resource
    MetricFilter:
      Tags:
        - Key: Environment
          Value: prod
    AlarmActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p0
    OKActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p0
    Statistic: Average
    Period: 60
    EvaluationPeriods: 3
    Threshold: 1.5
    ComparisonOperator: GreaterThanOrEqualToThreshold
    TreatMissingData: notBreaching
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
    CustomParameters:
      - Regex: saku-prod-api-cms
        Threshold: 2.0
        AlarmActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p1
        OKActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p1
  # Application ELB
  - MetricName: UnHealthyHostCount
    Namespace: AWS/ApplicationELB
    Dimensions:
      - Name: LoadBalancer
        ExtractRegex: (?<=app/)(?P<VALUE>.*)(?=/.*)
      - Name: TargetGroup
        ExtractRegex: (?<=targetgroup/)(?P<VALUE>.*)(?=/.*)
    Reference: Resource
    MetricFilter:
      Tags:
        - Key: Environment
          Value: prod
    AlarmActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p0
    OKActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p0
    Statistic: Maximum
    Period: 60
    EvaluationPeriods: 10
    Threshold: 0
    ComparisonOperator: GreaterThanThreshold
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
  # Application ELB
  - MetricName: HTTPCode_Target_5XX_Count
    Namespace: AWS/ApplicationELB
    Dimensions:
      - Name: LoadBalancer
        ExtractRegex: (?<=app/)(?P<VALUE>.*)(?=/.*)
      - Name: TargetGroup
        ExtractRegex: (?<=targetgroup/)(?P<VALUE>.*)(?=/.*)
    Reference: Resource
    MetricFilter:
      Tags:
        - Key: Environment
          Value: prod
    AlarmActions:
      - arn:aws:sns:ap-northeast-1:************:chatbot_slack_p0
    OKActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p0
    Statistic: Sum
    Period: 60
    EvaluationPeriods: 2
    Threshold: 250.0
    ComparisonOperator: GreaterThanOrEqualToThreshold
    TreatMissingData: notBreaching
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
    CustomParameters:
      - Regex: \bsaku-prod-api-rm\b
        EvaluationPeriods: 3
      - Regex: \bsaku-prod-api-cms\b
        EvaluationPeriods: 3
  # ECS
  - MetricName: CPUUtilization
    Namespace: AWS/ECS
    Dimensions:
      - Name: ClusterName
      - Name: ServiceName
    Reference: Resource
    MetricFilter:
      Tags:
        - Key: Environment
          Value: prod
    AlarmActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p0
    OKActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p0
    Statistic: Average
    Period: 60
    EvaluationPeriods: 3
    Threshold: 90.0
    ComparisonOperator: GreaterThanOrEqualToThreshold
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
  # ECS
  - MetricName: CPUUtilization
    Namespace: AWS/ECS
    Dimensions:
      - Name: ClusterName
      - Name: ServiceName
    Reference: Resource
    MetricFilter:
      Regexes:
        - prod-.*-campaign
    AlarmActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p0
    OKActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p0
    Statistic: Average
    Period: 60
    EvaluationPeriods: 3
    Threshold: 90.0
    ComparisonOperator: GreaterThanOrEqualToThreshold
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
  # ECS
  - MetricName: MemoryUtilization
    Namespace: AWS/ECS
    Dimensions:
      - Name: ClusterName
      - Name: ServiceName
    Reference: Resource
    MetricFilter:
      Tags:
        - Key: Environment
          Value: saku-prod
    AlarmActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p0
    OKActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p0
    Statistic: Average
    Period: 60
    EvaluationPeriods: 3
    Threshold: 80.0
    ComparisonOperator: GreaterThanOrEqualToThreshold
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
  # ECS
  - MetricName: MemoryUtilization
    Namespace: AWS/ECS
    Dimensions:
      - Name: ClusterName
      - Name: ServiceName
    Reference: Resource
    MetricFilter:
      Regexes:
        - prod-.*-campaign
    AlarmActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p0
    OKActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p0
    Statistic: Average
    Period: 60
    EvaluationPeriods: 3
    Threshold: 80.0
    ComparisonOperator: GreaterThanOrEqualToThreshold
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
  # Aurora
  - MetricName: CPUUtilization
    Namespace: AWS/RDS
    Dimensions:
      - Name: DBInstanceIdentifier
    Reference: Resource
    MetricFilter:
      Tags:
        - Key: Environment
          Value: prod
    AlarmActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p0
    OKActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p0
    Statistic: Average
    Period: 60
    EvaluationPeriods: 1
    Threshold: 90.0
    ComparisonOperator: GreaterThanOrEqualToThreshold
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
    CustomParameters:
      - Regex: DBInstanceIdentifier:saku-prod-db-\S*
        EvaluationPeriods: 3
        AlarmActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p1
        OKActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p1
  # Aurora
  - MetricName: DatabaseConnections
    Namespace: AWS/RDS
    Dimensions:
      - Name: DBInstanceIdentifier
    Reference: Resource
    MetricFilter:
      Tags:
        - Key: Environment
          Value: prod
    AlarmActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p0
    OKActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p0
    Statistic: Average
    Period: 60
    EvaluationPeriods: 3
    Threshold: builtin
    ComparisonOperator: GreaterThanOrEqualToThreshold
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
    CustomParameters:
      - Regex: .*
        ThresholdRate: 0.9
  # Aurora
  - MetricName: DiskQueueDepth
    Namespace: AWS/RDS
    Dimensions:
      - Name: DBInstanceIdentifier
    Reference: Resource
    MetricFilter:
      Tags:
        - Key: Environment
          Value: prod
    AlarmActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p1
    OKActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p1
    Statistic: Average
    Period: 60
    EvaluationPeriods: 5
    Threshold: 10.0
    ComparisonOperator: GreaterThanOrEqualToThreshold
    TreatMissingData: notBreaching
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
  # Aurora
  - MetricName: AuroraReplicaLag
    Namespace: AWS/RDS
    Dimensions:
      - Name: DBInstanceIdentifier
    Reference: Resource
    MetricFilter:
      Tags:
        - Key: Environment
          Value: prod
    AlarmActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p0
    OKActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p0
    Statistic: Average
    Period: 60
    EvaluationPeriods: 10
    Threshold: 30.0
    ComparisonOperator: GreaterThanOrEqualToThreshold
    TreatMissingData: notBreaching
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
    CustomParameters:
      - Regex: DBInstanceIdentifier:saku-prod-db\s  # writer
        AlarmExcluded: True
  # Aurora
  - MetricName: RollbackSegmentHistoryListLength
    Namespace: AWS/RDS
    Dimensions:
      - Name: DBClusterIdentifier
        Value: saku-prod-db-cluster
      - Name: Role
        Value: WRITER
    Reference: Resource
    AlarmDescription: "Please ask JPOC to run normality check and call relative BackEnd Engineers; than join the meeting to see if team needs further help. \nSRE SOP: https://kkvideo.atlassian.net/wiki/spaces/VPSRE/pages/3772809315"
    AlarmActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p0
    OKActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p0
    Statistic: Average
    Period: 60
    EvaluationPeriods: 1
    Threshold: 5000000
    ComparisonOperator: GreaterThanOrEqualToThreshold
    TreatMissingData: notBreaching
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
  # RDS Proxy
  - MetricName: AvailabilityPercentage
    Namespace: AWS/RDS
    Dimensions:
      - Name: ProxyName
      - Name: TargetGroup
      - Name: TargetRole
    Reference: Resource
    MetricFilter: # RDS Proxy does not have tag feature.
      Regexes:
        - saku-prod
    AlarmActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p1
    OKActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p1
    Statistic: Average
    Period: 60
    EvaluationPeriods: 5
    Threshold: 90
    ComparisonOperator: LessThanThreshold
    TreatMissingData: notBreaching
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
    TerminationProtection: True
  # EC2 Instance
  - MetricName: DiskSpaceUtilization
    Namespace: System/Linux
    Dimensions:
      - Name: InstanceId
      - Name: Filesystem
        Hidden: True
      - Name: MountPath
        Hidden: True
    Reference: Resource
    MetricFilter:
      Regexes:
        - Name:prod-[a-zA-Z0-9]
    AlarmActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p1
    OKActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p1
    Statistic: Average
    Period: 60
    EvaluationPeriods: 3
    Threshold: 80.0
    ComparisonOperator: GreaterThanOrEqualToThreshold
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
    CustomParameters:
      - Regex: jump\b
        AlarmExcluded: True
  # EC2 Instance
  - MetricName: MemoryUtilization
    Namespace: System/Linux
    Dimensions:
      - Name: InstanceId
    Reference: Resource
    MetricFilter:
      Regexes:
        - Name:prod-[a-zA-Z0-9]
    AlarmActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p1
    OKActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p1
    Statistic: Average
    Period: 60
    EvaluationPeriods: 3
    Threshold: 80.0
    ComparisonOperator: GreaterThanOrEqualToThreshold
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
    CustomParameters:
      - Regex: jump\b
        AlarmExcluded: True
  # ElasticCache Redis
  - MetricName: EngineCPUUtilization
    Namespace: AWS/ElastiCache
    Dimensions:
      - Name: CacheClusterId
      - Name: CacheNodeId
    Reference: Resource
    MetricFilter:
      Tags:
        - Key: Environment
          Value: saku-prod
    AlarmActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p0
    OKActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p0
    Statistic: Average
    Period: 60
    EvaluationPeriods: 5
    Threshold: 98
    ComparisonOperator: GreaterThanOrEqualToThreshold
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
    CustomParameters:
      - Regex: saku-prod-redis # This Redis cluster will be removed in 2024 Q2, and this custom parameter should be removed at the same time. Details in SP-2089
        AlarmActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p1
        OKActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p1
      - Regex: prod-cache
        Threshold: 90
  # ElasticCache Redis
  - MetricName: CPUUtilization
    Namespace: AWS/ElastiCache
    Dimensions:
      - Name: CacheClusterId
      - Name: CacheNodeId
    Reference: Resource
    MetricFilter:
      Tags:
        - Key: Environment
          Value: saku-prod
    AlarmActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p1
    OKActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p1
    Statistic: Average
    Period: 60
    EvaluationPeriods: 5
    Threshold: 60.0
    ComparisonOperator: GreaterThanOrEqualToThreshold
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
  # ElasticCache Redis
  - MetricName: BytesUsedForCache
    Namespace: AWS/ElastiCache
    Dimensions:
      - Name: CacheClusterId
    Reference: Resource
    MetricFilter:
      Tags:
        - Key: Environment
          Value: saku-prod
    AlarmActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p1
    OKActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p1
    Statistic: Average
    Period: 60
    EvaluationPeriods: 3
    Threshold: builtin
    ComparisonOperator: GreaterThanOrEqualToThreshold
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
    CustomParameters:
      - Regex: .*
        ThresholdRate: 0.8
  # DirectConnect
  - MetricName: ConnectionState
    Namespace: AWS/DX
    Dimensions:
      - Name: ConnectionId
        Hidden: True
    Reference: Resource
    AlarmActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_directconnect
    OKActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_directconnect
    Statistic: Average
    Period: 60
    EvaluationPeriods: 1
    Threshold: 1
    ComparisonOperator: LessThanThreshold
    TreatMissingData: notBreaching
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
  # DirectConnect
  - MetricName: ConnectionBpsIngress
    Namespace: AWS/DX
    Dimensions:
      - Name: ConnectionId
        Hidden: True
    Reference: Resource
    AlarmActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p2
    OKActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p2
    Statistic: Average
    Period: 60
    EvaluationPeriods: 3
    Threshold: 20000000
    ComparisonOperator: LessThanThreshold
    TreatMissingData: missing
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
  # DirectConnect
  - MetricName: ConnectionPpsIngress
    Namespace: AWS/DX
    Dimensions:
      - Name: ConnectionId
        Hidden: True
    Reference: Resource
    AlarmActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p2
    OKActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p2
    Statistic: Average
    Period: 60
    EvaluationPeriods: 3
    Threshold: 51600
    ComparisonOperator: LessThanOrEqualToThreshold
    TreatMissingData: missing
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
  # DirectConnect
  - MetricName: ConnectionBpsEgress
    Namespace: AWS/DX
    Dimensions:
      - Name: ConnectionId
        Hidden: True
    Reference: Resource
    AlarmActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p2
    OKActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p2
    Statistic: Average
    Period: 60
    EvaluationPeriods: 3
    Threshold: 600000000
    ComparisonOperator: GreaterThanThreshold
    TreatMissingData: missing
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
  # DirectConnect
  - MetricName: ConnectionPpsEgress
    Namespace: AWS/DX
    Dimensions:
      - Name: ConnectionId
        Hidden: True
    Reference: Resource
    AlarmActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p2
    OKActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p2
    Statistic: Average
    Period: 60
    EvaluationPeriods: 3
    Threshold: 0
    ComparisonOperator: LessThanOrEqualToThreshold
    TreatMissingData: missing
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
  # 3rd-party API Request Count
  - MetricName: PrepApigwRequestCount
    Namespace: CustomMetrics
    Reference: CloudWatch
    AlarmActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_apigw_pid_request_count
    OKActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_apigw_pid_request_count
    Statistic: Maximum
    Period: 300
    EvaluationPeriods: 1
    Threshold: 1800.0
    ComparisonOperator: GreaterThanOrEqualToThreshold
    TreatMissingData: notBreaching
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
  # 3rd-party API Request Count
  - MetricName: PrepPidRequestCount
    Namespace: CustomMetrics
    Reference: CloudWatch
    AlarmActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_apigw_pid_request_count
    OKActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_apigw_pid_request_count
    Statistic: Maximum
    Period: 300
    EvaluationPeriods: 1
    Threshold: 400.0
    ComparisonOperator: GreaterThanOrEqualToThreshold
    TreatMissingData: notBreaching
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
  # 3rd-party API Request Count
  - MetricName: ProdApigwRequestCount
    Namespace: CustomMetrics
    Reference: CloudWatch
    AlarmActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p1
    OKActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p1
    Statistic: Maximum
    Period: 300
    EvaluationPeriods: 1
    Threshold: 18000.0
    ComparisonOperator: GreaterThanOrEqualToThreshold
    TreatMissingData: notBreaching
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
  # MediaLive
  - AlarmId: MediaLive NetworkIn
    Metrics:
      - Id: m1
        MetricStat:
          Metric:
            Namespace: AWS/MediaLive
            MetricName: NetworkIn
            Dimensions:
              - Name: ChannelId
                Replace:
                  - Value: "6785586"
                    Alias: saku-prod-01
                  - Value: "7071053"
                    Alias: saku-prod-02
                  - Value: "3664896"
                    Alias: saku-prod-03
                  - Value: "3409475"
                    Alias: saku-prod-04
                  - Value: "21346"
                    Alias: saku-prod-05
                  - Value: "8079792"
                    Alias: saku-prod-06
                  - Value: "2522853"
                    Alias: saku-prod-07
                  - Value: "3236033"
                    Alias: saku-prod-08
                  - Value: "4721040"
                    Alias: saku-prod-09
                  - Value: "8395824"
                    Alias: saku-prod-10
                  - Value: "6059274"
                    Alias: saku-prod-11
                  - Value: "3103748"
                    Alias: saku-prod-12
                  - Value: "9412978"
                    Alias: saku-prod-13
                  - Value: "1894750"
                    Alias: saku-prod-14
                  - Value: "5586077"
                    Alias: saku-prod-15
                  - Value: "8641494"
                    Alias: saku-prod-16
                  - Value: "1164485"
                    Alias: saku-prod-26
                  - Value: "3974762"
                    Alias: saku-prod-27
                  - Value: "2248429"
                    Alias: saku-prod-28
                  - Value: "2760527"
                    Alias: saku-prod-29
                  - Value: "4930466"
                    Alias: saku-prod-30
                  - Value: "8860657"
                    Alias: saku-prod-31
                  - Value: "7852673"
                    Alias: saku-prod-32
                  - Value: "8018217"
                    Alias: saku-prod-33
                  - Value: "7952010"
                    Alias: saku-prod-34
                  - Value: "6203703"
                    Alias: saku-prod-35
                  - Value: "236150"
                    Alias: saku-prod-36
                  - Value: "9415076"
                    Alias: saku-prod-37
                  - Value: "90400"
                    Alias: saku-prod-38
                  - Value: "4393549"
                    Alias: saku-prod-40
                  - Value: "7509089"
                    Alias: saku-prod-41
                  - Value: "2794946"
                    Alias: saku-prod-42
                  - Value: "748357"
                    Alias: saku-prod-43
                  - Value: "626661"
                    Alias: saku-prod-44
                  - Value: "6955409"
                    Alias: saku-prod-45
                  - Value: "2974563"
                    Alias: saku-prod-46
                  - Value: "7503890"
                    Alias: saku-prod-47
                  - Value: "2307012"
                    Alias: saku-prod-48
                  - Value: "7756127"
                    Alias: saku-prod-49
              - Name: Pipeline
          Period: 60
          Stat: Average
        ReturnData: True
        AccountId: "************"
    AlarmActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p2
    OKActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p2
    EvaluationPeriods: 3
    Threshold: 15
    ComparisonOperator: LessThanThreshold
    TreatMissingData: missing
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
    TerminationProtection: True
    CustomParameters:
     - Regex: ChannelId:\d+
       AlarmExcluded: True
  # MediaLive
  - AlarmId: MediaLive Networkout
    Metrics:
      - Id: m1
        MetricStat:
          Metric:
            Namespace: AWS/MediaLive
            MetricName: NetworkOut
            Dimensions:
              - Name: ChannelId
                Replace:
                  - Value: "6785586"
                    Alias: saku-prod-01
                  - Value: "7071053"
                    Alias: saku-prod-02
                  - Value: "3664896"
                    Alias: saku-prod-03
                  - Value: "3409475"
                    Alias: saku-prod-04
                  - Value: "21346"
                    Alias: saku-prod-05
                  - Value: "8079792"
                    Alias: saku-prod-06
                  - Value: "2522853"
                    Alias: saku-prod-07
                  - Value: "3236033"
                    Alias: saku-prod-08
                  - Value: "4721040"
                    Alias: saku-prod-09
                  - Value: "8395824"
                    Alias: saku-prod-10
                  - Value: "6059274"
                    Alias: saku-prod-11
                  - Value: "3103748"
                    Alias: saku-prod-12
                  - Value: "9412978"
                    Alias: saku-prod-13
                  - Value: "1894750"
                    Alias: saku-prod-14
                  - Value: "5586077"
                    Alias: saku-prod-15
                  - Value: "8641494"
                    Alias: saku-prod-16
                  - Value: "1164485"
                    Alias: saku-prod-26
                  - Value: "3974762"
                    Alias: saku-prod-27
                  - Value: "2248429"
                    Alias: saku-prod-28
                  - Value: "2760527"
                    Alias: saku-prod-29
                  - Value: "4930466"
                    Alias: saku-prod-30
                  - Value: "8860657"
                    Alias: saku-prod-31
                  - Value: "7852673"
                    Alias: saku-prod-32
                  - Value: "8018217"
                    Alias: saku-prod-33
                  - Value: "7952010"
                    Alias: saku-prod-34
                  - Value: "6203703"
                    Alias: saku-prod-35
                  - Value: "236150"
                    Alias: saku-prod-36
                  - Value: "9415076"
                    Alias: saku-prod-37
                  - Value: "90400"
                    Alias: saku-prod-38
                  - Value: "4393549"
                    Alias: saku-prod-40
                  - Value: "7509089"
                    Alias: saku-prod-41
                  - Value: "2794946"
                    Alias: saku-prod-42
                  - Value: "748357"
                    Alias: saku-prod-43
                  - Value: "626661"
                    Alias: saku-prod-44
                  - Value: "6955409"
                    Alias: saku-prod-45
                  - Value: "2974563"
                    Alias: saku-prod-46
                  - Value: "7503890"
                    Alias: saku-prod-47
                  - Value: "2307012"
                    Alias: saku-prod-48
                  - Value: "7756127"
                    Alias: saku-prod-49
              - Name: Pipeline
          Period: 60
          Stat: Average
        ReturnData: True
        AccountId: "************"
    AlarmActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p2
    OKActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p2
    EvaluationPeriods: 3
    Threshold: 5
    ComparisonOperator: LessThanThreshold
    TreatMissingData: missing
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
    TerminationProtection: True
    CustomParameters:
     - Regex: ChannelId:\d+
       AlarmExcluded: True
  # MediaLive
  - AlarmId: MediaLive InputVideoFrameRate
    Metrics:
      - Id: m1
        MetricStat:
          Metric:
            Namespace: AWS/MediaLive
            MetricName: InputVideoFrameRate
            Dimensions:
              - Name: ChannelId
                Replace:
                  - Value: "6785586"
                    Alias: saku-prod-01
                  - Value: "7071053"
                    Alias: saku-prod-02
                  - Value: "3664896"
                    Alias: saku-prod-03
                  - Value: "3409475"
                    Alias: saku-prod-04
                  - Value: "21346"
                    Alias: saku-prod-05
                  - Value: "8079792"
                    Alias: saku-prod-06
                  - Value: "2522853"
                    Alias: saku-prod-07
                  - Value: "3236033"
                    Alias: saku-prod-08
                  - Value: "4721040"
                    Alias: saku-prod-09
                  - Value: "8395824"
                    Alias: saku-prod-10
                  - Value: "6059274"
                    Alias: saku-prod-11
                  - Value: "3103748"
                    Alias: saku-prod-12
                  - Value: "9412978"
                    Alias: saku-prod-13
                  - Value: "1894750"
                    Alias: saku-prod-14
                  - Value: "5586077"
                    Alias: saku-prod-15
                  - Value: "8641494"
                    Alias: saku-prod-16
                  - Value: "1164485"
                    Alias: saku-prod-26
                  - Value: "3974762"
                    Alias: saku-prod-27
                  - Value: "2248429"
                    Alias: saku-prod-28
                  - Value: "2760527"
                    Alias: saku-prod-29
                  - Value: "4930466"
                    Alias: saku-prod-30
                  - Value: "8860657"
                    Alias: saku-prod-31
                  - Value: "7852673"
                    Alias: saku-prod-32
                  - Value: "8018217"
                    Alias: saku-prod-33
                  - Value: "7952010"
                    Alias: saku-prod-34
                  - Value: "6203703"
                    Alias: saku-prod-35
                  - Value: "236150"
                    Alias: saku-prod-36
                  - Value: "9415076"
                    Alias: saku-prod-37
                  - Value: "90400"
                    Alias: saku-prod-38
                  - Value: "4393549"
                    Alias: saku-prod-40
                  - Value: "7509089"
                    Alias: saku-prod-41
                  - Value: "2794946"
                    Alias: saku-prod-42
                  - Value: "748357"
                    Alias: saku-prod-43
                  - Value: "626661"
                    Alias: saku-prod-44
                  - Value: "6955409"
                    Alias: saku-prod-45
                  - Value: "2974563"
                    Alias: saku-prod-46
                  - Value: "7503890"
                    Alias: saku-prod-47
                  - Value: "2307012"
                    Alias: saku-prod-48
                  - Value: "7756127"
                    Alias: saku-prod-49
              - Name: Pipeline
          Period: 60
          Stat: Average
        ReturnData: True
        AccountId: "************"
    AlarmActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p2
    OKActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p2
    EvaluationPeriods: 3
    Threshold: 24
    ComparisonOperator: LessThanOrEqualToThreshold
    TreatMissingData: missing
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
    TerminationProtection: True
    CustomParameters:
     - Regex: ChannelId:\d+
       AlarmExcluded: True
  # MediaLive
  - AlarmId: MediaLive DroppedFrames
    Metrics:
      - Id: m1
        MetricStat:
          Metric:
            Namespace: AWS/MediaLive
            MetricName: DroppedFrames
            Dimensions:
              - Name: ChannelId
                Replace:
                  - Value: "6785586"
                    Alias: saku-prod-01
                  - Value: "7071053"
                    Alias: saku-prod-02
                  - Value: "3664896"
                    Alias: saku-prod-03
                  - Value: "3409475"
                    Alias: saku-prod-04
                  - Value: "21346"
                    Alias: saku-prod-05
                  - Value: "8079792"
                    Alias: saku-prod-06
                  - Value: "2522853"
                    Alias: saku-prod-07
                  - Value: "3236033"
                    Alias: saku-prod-08
                  - Value: "4721040"
                    Alias: saku-prod-09
                  - Value: "8395824"
                    Alias: saku-prod-10
                  - Value: "6059274"
                    Alias: saku-prod-11
                  - Value: "3103748"
                    Alias: saku-prod-12
                  - Value: "9412978"
                    Alias: saku-prod-13
                  - Value: "1894750"
                    Alias: saku-prod-14
                  - Value: "5586077"
                    Alias: saku-prod-15
                  - Value: "8641494"
                    Alias: saku-prod-16
                  - Value: "1164485"
                    Alias: saku-prod-26
                  - Value: "3974762"
                    Alias: saku-prod-27
                  - Value: "2248429"
                    Alias: saku-prod-28
                  - Value: "2760527"
                    Alias: saku-prod-29
                  - Value: "4930466"
                    Alias: saku-prod-30
                  - Value: "8860657"
                    Alias: saku-prod-31
                  - Value: "7852673"
                    Alias: saku-prod-32
                  - Value: "8018217"
                    Alias: saku-prod-33
                  - Value: "7952010"
                    Alias: saku-prod-34
                  - Value: "6203703"
                    Alias: saku-prod-35
                  - Value: "236150"
                    Alias: saku-prod-36
                  - Value: "9415076"
                    Alias: saku-prod-37
                  - Value: "90400"
                    Alias: saku-prod-38
                  - Value: "4393549"
                    Alias: saku-prod-40
                  - Value: "7509089"
                    Alias: saku-prod-41
                  - Value: "2794946"
                    Alias: saku-prod-42
                  - Value: "748357"
                    Alias: saku-prod-43
                  - Value: "626661"
                    Alias: saku-prod-44
                  - Value: "6955409"
                    Alias: saku-prod-45
                  - Value: "2974563"
                    Alias: saku-prod-46
                  - Value: "7503890"
                    Alias: saku-prod-47
                  - Value: "2307012"
                    Alias: saku-prod-48
                  - Value: "7756127"
                    Alias: saku-prod-49
              - Name: Pipeline
          Period: 60
          Stat: Average
        ReturnData: True
        AccountId: "************"
    AlarmActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p2
    OKActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p2
    EvaluationPeriods: 1
    Threshold: 1
    ComparisonOperator: GreaterThanThreshold
    TreatMissingData: missing
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
    TerminationProtection: True
    CustomParameters:
     - Regex: ChannelId:\d+
       AlarmExcluded: True
  # Aggregated Aurora
  - AlarmId: saku-production-db reader CPUUtilization
    AlarmActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p0
    OKActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p0
    AlarmFilters:
      - Prefix: auto
        Regex: DBInstanceIdentifier:saku-prod-db-1[a-c]-r\d+ CPUUtilization
    RuleFunction: AllInAlarm
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
  # ElasticCache Redis
  - MetricName: EngineCPUUtilization
    Namespace: AWS/ElastiCache
    Dimensions:
      - Name: CacheClusterId
      - Name: CacheNodeId
    Reference: Resource
    MetricFilter:
      Regexes:
        - prod-cache
    AlarmActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p1
    OKActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p1
    Stat: Average
    Period: 60
    Stdev: 2
    EvaluationPeriods: 5
    ComparisonOperator: GreaterThanUpperThreshold
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
  # AutoScaling policy hook
  - AlarmId: ClientErrors
    LogGroupNames:
      - /aws/lambda/prod-saku-chatops-ecs-autoscaling
      - /aws/lambda/prod-saku-chatops-ecs-renew-autoscaling-policy
    FilterPattern: "{$.level=ERROR && $.message.cluster=%prod%}"
    MetricValue: 1
    Dimensions:
      - Name: cluster
        Value: $.message.cluster
      - Name: service
        Value: $.message.service
    AlarmDescription: Please follow the instructions in https://kkvideo.atlassian.net/wiki/spaces/VPSRE/pages/3364258039/Saku+Setup+a+monitoring+method+to+track+the+ECS+hook
    AlarmActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_chatops
    Statistic: Average
    Period: 60
    EvaluationPeriods: 1
    DatapointsToAlarm: 1
    Threshold: 0
    ComparisonOperator: GreaterThanThreshold
    TreatMissingData: notBreaching
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
    TerminationProtection: True
  # ECS
  - MetricName: OutOfMemory
    Namespace: Custom/Log
    Dimensions:
      - Name: Cluster
        ExtractRegex: arn:aws:ecs:[-\w]*?:\d+?:cluster/(?P<VALUE>.*)
      - Name: Service
        ExtractRegex: (?<=service:)(?P<VALUE>.*)
    Reference: CloudWatch
    AlarmDescription: The ECS service detected OOM tasks. Please investigate whether the service is unstable.
    AlarmActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p1
    OKActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p1
    Statistic: Sum
    Period: 300
    EvaluationPeriods: 1
    Threshold: 3
    ComparisonOperator: GreaterThanOrEqualToThreshold
    TreatMissingData: notBreaching
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
    TerminationProtection: True
  # SQS
  - MetricName: ApproximateAgeOfOldestMessage
    Namespace: AWS/SQS
    Dimensions:
      - Name: QueueName
        Value: kks-prod-saku-search-injection-dlq
    AlarmDescription: "<!subteam^SLT8046SF> to create the P2 ST ticket for BE to do investigate."  # <!subteam^SLT8046SF> is JPOC
    AlarmActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p1_saku_jpoc_error
    OKActions: arn:aws:sns:ap-northeast-1:************:chatbot_slack_p1_saku_jpoc_error
    Statistic: Average
    Period: 60
    Threshold: 0
    EvaluationPeriods: 1
    ComparisonOperator: GreaterThanThreshold
    TreatMissingData: notBreaching
    Tags:
      - Key: Environment
        Value: prod
      - Key: EnvName
        Value: saku-prod
    TerminationProtection: True
