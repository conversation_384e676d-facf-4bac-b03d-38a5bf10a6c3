#!/usr/bin/env python3

#############################
# AWS CDK
#############################
from aws_cdk import App
from aws_cdk import Environment
from kks.cdk_construct import query_context


#############################
# Stack
#############################
from stack.chatops_stack import ChatOpsStack
from stack.police_stack import PoliceStack
from stack.system_manager_stack import SystemManagerStack


#############################
# Construct
#############################
app = App()

stack_env = Environment(
    account=query_context(app, "aws.account"),
    region=query_context(app, "aws.region")
)

chatops_stack = ChatOpsStack(
    app, "{env}-{product}-{service}".format(
        env=query_context(app, "tags.Environment"),
        product=query_context(app, "tags.Product"),
        service=query_context(app, "tags.Service")
    ),
    env=stack_env,
    termination_protection=query_context(app, "stack.termination_protection")
)

ssm_stack = SystemManagerStack(
    app, "{env}-{product}-{service}-ssm".format(
        env=query_context(app, "tags.Environment"),
        product=query_context(app, "tags.Product"),
        service=query_context(app, "tags.Service")

    ),
    env=stack_env,
    termination_protection=query_context(app, "stack.termination_protection")
)

police_stack = PoliceStack(
    app, "{env}-{product}-{service}-police".format(
        env=query_context(app, "tags.Environment"),
        product=query_context(app, "tags.Product"),
        service=query_context(app, "tags.Service")
    ),
    env=stack_env,
    termination_protection=query_context(app, "stack.termination_protection")
)

app.synth()
