#############################
# AWS CDK
#############################
from aws_cdk import Stack
from aws_cdk import aws_iam
from aws_cdk import aws_sns
from constructs import Construct
from kks.cdk_construct import query_context
from kks.cdk_construct.chatbot_patterns import ProjectSlackChannels


#############################
# CloudFormation Stack
#############################
class ChatOpsStack(Stack):

    def __init__(self, scope: Construct, construct_id: str, **kwargs) -> None:
        super().__init__(scope, construct_id, **kwargs)

        self.prefix = "{env}-{product}-{service}".format(
            env=query_context(scope, "tags.Environment"),
            product=query_context(scope, "tags.Product"),
            service=query_context(scope, "tags.Service")
        )

        # create AWS Chatbot Slack channels
        channels = ProjectSlackChannels.from_cdk_context(self)
        self.chatbot_sns: aws_sns.Topic = channels["chatops"].default_sns_topic
        # allow Step Functions events to publish messages to AWS Chatbot channels
        self.chatbot_sns.add_to_resource_policy(aws_iam.PolicyStatement(
            principals=[aws_iam.AnyPrincipal()],
            actions=[
                "SNS:GetTopicAttributes",
                "SNS:SetTopicAttributes",
                "SNS:AddPermission",
                "SNS:RemovePermission",
                "SNS:DeleteTopic",
                "SNS:Subscribe",
                "SNS:ListSubscriptionsByTopic",
                "SNS:Publish"
            ],
            resources=[self.chatbot_sns.topic_arn],
            conditions={
                "StringEquals": {
                    "AWS:SourceAccount": self.account
                }
            }
        ))

        # grant chatbot role permission
        chatbot_role: aws_iam.Role = channels["chatops"].role
        retval = chatbot_role.add_to_policy(
            aws_iam.PolicyStatement(
                sid="InvokeLambdaFromSlack",
                effect=aws_iam.Effect.ALLOW,
                actions=[
                    "lambda:InvokeFunction",
                    "lambda:InvokeAsync"
                ],
                resources=["*"]
            )
        )
        assert retval, "Failed to add permission lambda:Invoke* to policy"

        # grant for step functions
        retval = chatbot_role.add_to_policy(
            aws_iam.PolicyStatement(
                effect=aws_iam.Effect.ALLOW,
                actions=[
                    "states:StartExecution"
                ],
                resources=["*"]
            )
        )
        assert retval, "Failed to add permission states:StartExecution to policy"
