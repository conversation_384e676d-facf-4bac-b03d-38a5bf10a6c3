from pathlib import PurePath


#############################
# AWS CDK
#############################
from aws_cdk import Duration
from aws_cdk import Stack
from aws_cdk import aws_iam
from aws_cdk import aws_logs
from aws_cdk import aws_scheduler
from constructs import Construct
from kks.cdk_construct.lambda_patterns import ScheduledFunction
from kks.cdk_construct import query_context


#############################
# CloudFormation Stack
#############################
class PoliceStack(Stack):

    def __init__(self, scope: Construct, construct_id: str, **kwargs) -> None:
        super().__init__(scope, construct_id, **kwargs)

        self.prefix = "{env}-{product}-{service}".format(
            env=query_context(scope, "tags.Environment"),
            product=query_context(scope, "tags.Product"),
            service=query_context(scope,"tags.Service")
        )
        env = query_context(scope, "tags.Environment")

        # Lambda function for alarm police
        schedule = ScheduledFunction(
            self, "AlarmPolice",
            function_name=f"{self.prefix}-system-alarm-police",
            entry=PurePath("lambda_function", "alarm_police", "src"),
            handler="main.run",
            description="Trigger Alarm Police every 10 minutes",
            memory_size=170,
            timeout=300,
            log_retention=aws_logs.RetentionDays.ONE_MONTH,
            schedule=aws_scheduler.ScheduleExpression.rate(Duration.minutes(10)),
            retry_attempts=0,
        )
        function = schedule.function
        function.role.add_managed_policy(
            aws_iam.ManagedPolicy.from_aws_managed_policy_name("CloudWatchFullAccessV2")
        )
        function.role.add_managed_policy(
            aws_iam.ManagedPolicy.from_aws_managed_policy_name("ReadOnlyAccess")
        )
        function.add_environment(
            key="FEATURE_SUPPRESS_NOTIFICATION_ON_INIT",
            value="true"
        )
        function.add_environment(
            key="POWERTOOLS_LOG_LEVEL",
            value="INFO"
        )
        function.add_environment(
            key="POWERTOOLS_SERVICE_NAME",
            value="police"
        )

        # Case: CodeDeploy -> EventBridge event -> SNS -> Lambda
        # In Saku project, the alarm police function is triggered every 5 minutes, so no need to use this.
        # if topic_names := query_context(scope, "ecs_autoscaling.topics"):
        #     for index, topic_name in enumerate(topic_names):
        #         topic_ecs_codedeploy_status_change = aws_sns.Topic.from_topic_arn(
        #             self, f"TopicEcsCodeDeployStatusChange{index}",
        #             topic_arn=f"arn:aws:sns:{self.region}:{self.account}:{topic_name}"
        #         )
        #         topic_ecs_codedeploy_status_change.add_subscription(
        #             aws_sns_subscriptions.LambdaSubscription(function_alarm_police)
        #         )
