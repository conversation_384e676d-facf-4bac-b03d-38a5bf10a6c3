#############################
# AWS CDK
#############################
from aws_cdk import RemovalPolicy
from aws_cdk import Stack
from aws_cdk import aws_ssm
from constructs import Construct
from kks.cdk_construct import query_context


#############################
# CloudFormation Stack
#############################
class SystemManagerStack(Stack):

    def __init__(self, scope: Construct, construct_id: str, **kwargs) -> None:
        super().__init__(scope, construct_id, **kwargs)

        self.prefix = "{env}-{product}-{service}".format(
            env=query_context(scope, "tags.Environment"),
            product=query_context(scope, "tags.Product"),
            service=query_context(scope, "tags.Service")
        )

        # create Systems Manager document
        document = aws_ssm.CfnDocument(
            self, "RestartApiServiceDocument",
            name="Chatops-RestartApiService",
            document_type="Command",
            document_format="JSON",
            target_type="/AWS::EC2::Instance",
            content={
                "schemaVersion": "2.2",
                "description": "Restart API service",
                "mainSteps": [
                    {
                        "action": "aws:runShellScript",
                        "name": "runCommands",
                        "inputs": {
                            "timeoutSeconds": "30",
                            "runCommand": [
                                "systemctl restart api",
                                "systemctl status api"
                            ]
                        }
                    }
                ]
            }
        )
        document.apply_removal_policy(RemovalPolicy.DESTROY)
