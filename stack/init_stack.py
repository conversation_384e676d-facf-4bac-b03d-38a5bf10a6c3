#############################
# AWS CDK
#############################
from aws_cdk import RemovalPolicy
from aws_cdk import Stack
from aws_cdk import Tags
from aws_cdk import aws_iam
from constructs import Construct
from kks.cdk_construct import query_context


#############################
# CloudFormation Stack
#############################
class InitStack(Stack):

    def __init__(self, scope: Construct, construct_id: str, **kwargs) -> None:
        super().__init__(scope, construct_id, **kwargs)

        self.prefix = "{product}-{service}".format(
            product=query_context(scope, "tags.Product"),
            service=query_context(scope, "tags.Service")
        )

        # create gitlab ci user
        ci_user = aws_iam.User(self, "BotUser")
        ci_user.apply_removal_policy(RemovalPolicy.DESTROY)
        ci_user.add_managed_policy(aws_iam.ManagedPolicy.from_aws_managed_policy_name("job-function/ViewOnlyAccess"))
        ci_user.add_to_policy(aws_iam.PolicyStatement(
            sid="PassRoleToCloudFormation",
            effect=aws_iam.Effect.ALLOW,
            actions=["iam:PassRole"],
            resources=["arn:aws:iam::*:role/cdk-hnb659fds-cfn-exec-role-*"]
        ))
        ci_user.add_to_policy(aws_iam.PolicyStatement(
            sid="AssumeRoleForCdk",
            effect=aws_iam.Effect.ALLOW,
            actions=["sts:AssumeRole"],
            resources=["arn:aws:iam::*:role/cdk-hnb659fds-*"]
        ))
        Tags.of(ci_user).add("GITLAB_GROUP", query_context(scope, "gitlab.group"))
        Tags.of(ci_user).add("GITLAB_PROJECT", query_context(scope, "gitlab.project"))
        Tags.of(ci_user).add("GITLAB_PROJECT_ID", query_context(scope, "gitlab.project_id"))
