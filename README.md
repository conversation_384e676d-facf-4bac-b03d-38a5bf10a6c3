# ChatOps for Slack

This project lets user send CLI commands from <PERSON><PERSON>ck through AWS Chatbot.

<!-- TOC -->

- [ChatOps for Slack](#chatops-for-slack)
  - [Prerequisites](#prerequisites)
  - [Get Started](#get-started)
  - [Before Deployment](#before-deployment)
  - [Useful commands](#useful-commands)
  - [Stacks](#stacks)
  - [Development](#development)
    - [Deploy to Dev Environment](#deploy-to-dev-environment)
    - [Deploy to Prod Environment](#deploy-to-prod-environment)
  - [Testing](#testing)
    - [Lambda Function](#lambda-function)
    - [Integration Test for Stacks](#integration-test-for-stacks)
  - [Component Diagrams](#component-diagrams)
  - [GitLab CI](#gitlab-ci)
  - [Troubleshooting](#troubleshooting)

<!-- /TOC -->

## Prerequisites

Before coding, you should configure client to Slack firstly: https://docs.aws.amazon.com/chatbot/latest/adminguide/getting-started.html#chat-client-setup

## Get Started

You need Python 3.9 or later to run this project.

Install or update nvm, see https://github.com/nvm-sh/nvm#installing-and-updating.

Install node, see https://github.com/nvm-sh/nvm#usage.

Install the AWS CDK Toolkit globally using the following Node Package Manager command.

```sh
$ npm install -g aws-cdk
```

The `cdk.json` file tells the CDK Toolkit how to execute your app.

This project is set up like a standard Python project.  The initialization
process also creates a virtualenv within this project, stored under the `.venv`
directory.  To create the virtualenv it assumes that there is a `python3`
(or `python` for Windows) executable in your path with access to the `venv`
package. If for any reason the automatic creation of the virtualenv fails,
you can create the virtualenv manually.

To manually create a virtualenv on MacOS and Linux:

```sh
$ python3 -m venv .venv
```

After the init process completes and the virtualenv is created, you can use the following
step to activate your virtualenv.

```sh
$ source .venv/bin/activate
```

If you are a Windows platform, you would activate the virtualenv like this:

```powershell
% .venv\Scripts\activate.bat
```

Once the virtualenv is activated, you can install the required dependencies.

```sh
$ pip install -r requirements.txt
```

Prepare your AWS CLI configuration, see https://docs.aws.amazon.com/cli/latest/userguide/cli-configure-files.html. If your profile name is **saku**, you can specify a profile in an environment variable for commands that run in the following session.

```sh
$ export AWS_PROFILE=saku
```

Recommend to use environment variable `AWS_PROFILE=saku` to specify credentials, not `cdk --profile=saku`, because **AWS_PROFILE** is available for most of AWS tools and packages, but **--profile** is only available on **cdk**.

You can develop CDK apps now.

## Before Deployment

For the first time, before you can deploy your AWS CDK app, you must bootstrap your AWS environment. This creates a staging bucket that the AWS CDK uses to deploy stacks containing assets, and creates an IAM user as service account for GitLab runner.

For dev environment,

```sh
$ cp cdk-dev.json cdk.json
$ npx cdk bootstrap
$ npx cdk deploy --app "python app-init.py" --require-approval never
```

For prod environment,

```sh
$ cp cdk-prod.json cdk.json
$ npx cdk bootstrap
$ npx cdk deploy --app "python app-init.py" --require-approval never
```

After the IAM user is ready, you must create an access key manually on AWS console, and then put the access key as GitLab CI variable.

## Useful commands

* `cdk ls`          list all stacks in the app
* `cdk synth`       emits the synthesized CloudFormation template
* `cdk deploy`      deploy this stack to your default AWS account/region
* `cdk diff`        compare deployed stack with current state
* `cdk docs`        open CDK documentation

## Stacks

 * prod-saku-chatops
 * prod-saku-chatops-ssm
 * prod-saku-chatops-ecs-autoscaling
 * prod-saku-chatops-police
 * prod-saku-chatops-promotion

## Development

### Deploy to Dev Environment

Before deploying to _dev_ environment on AWS account **backyard**, Change your AWS CLI configuration.

```sh
$ export AWS_PROFILE=backyard
$ cp cdk-dev.json cdk.json
$ npx cdk synth
$ npx cdk deploy dev-saku-chatops
```

### Deploy to Prod Environment

Before deploying to _prod_ environment on AWS account **saku**, Change your AWS CLI configuration.

```sh
$ export AWS_PROFILE=saku
$ cp cdk-prod.json cdk.json
$ npx cdk synth
$ npx cdk deploy prod-saku-chatops
```

## Testing

### Lambda Function

Follow the document to install Docker, see https://docs.docker.com/docker-for-mac/install/.

Follow the document to install AWS SAM CLI, see https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/serverless-sam-cli-install.html.

Follow the document to learn how to local run ths application, see https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/serverless-getting-started-hello-world.html.

### Integration Test for Stacks

After deployment, you can use Slack channel to send command to AWS chatbot.

## Component Diagrams

Uses CDK-Dia to generate _diagram.png_  for infrastructure. See https://www.npmjs.com/package/cdk-dia

```sh
# install packages
npm install -g cdk-dia
brew install graphviz

# generate
npx cdk-dia
```

## GitLab CI

Variables:

* AWS_ACCESS_KEY_ID
* AWS_SECRET_ACCESS_KEY

## Troubleshooting

1. If error message is _"failed bootstrapping: Error: Not downgrading existing bootstrap stack from version '5' to version '0'. Use --force to force."_, you should execute command `cdk bootstrap --force` where a new S3 bucket will be created, and then manually delete the legacy S3 bucket.

2. If error message is _"This CDK deployment requires bootstrap stack version '6', found '3'. Please run 'cdk bootstrap'"_, you should execute command `cdk bootstrap` to upgrade.

3. In _cdk.json_, there are a lot of feature flags, you can find desciptions in [features.ts](https://github.com/aws/aws-cdk/blob/master/packages/@aws-cdk/cx-api/lib/features.ts).

4. On AWS console, if you see the message _"The deployment package of your Lambda function \"\<your function name\>\" is too large to enable inline code editing. However, you can still invoke your function."_ on Lambda function code source page, this means the source code size is more than 3 MB. You can take advantage of Lambda layer to decrease source code size.

5. After deployed, you could discover there are other lambda functions created which name contains _LogRetention_. The reason is log group is created implicitly when a Lambda function starts to log. So CDK needs a custom resource to configure log retention. See the related issues
   * [Github: Setting retention for log groups of Lambdas](https://github.com/aws/aws-cdk/issues/248)
   * [Github: Make log groups for Lambda/CodeBuild accessible and/or manipulable](https://github.com/aws/aws-cdk/issues/667)
   * [Github: Unnecessary custom resource for Lambda log retention?](https://github.com/aws/aws-cdk/issues/11878)

6. All AWS service principals: https://gist.github.com/shortjared/4c1e3fe52bdfa47522cfe5b41e5d6f22
